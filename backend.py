from langchain_groq import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing_extensions import TypedDict, Annotated, Optional
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, AIMessage
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.prebuilt import create_react_agent
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain.agents import Tool
import sqlite3
import os
from dotenv import load_dotenv
load_dotenv()

def model_create(groq_api_key, model_name): 
    ## define state
    class State(TypedDict):
        messages: Annotated[list[BaseMessage], add_messages]

    search_tool = TavilySearchResults()

    def formatted_web_search(query: str) -> str:
        raw_results = search_tool.run(query)
        
        if not raw_results:
            return "No current information found for this query."
        
        formatted = f"Here's what I found about '{query}':\n\n"
        
        for i, result in enumerate(raw_results[:3], 1):
            title = result.get("title", "")
            content = result.get("content", "")[:200] + "..."
            url = result.get("url", "")
            
            formatted += f"**{i}. {title}**\n{content}\n*Source: {url}*\n\n"
        
        return formatted
    
    custom_search = Tool(
        name="web_search",
        func=formatted_web_search,
        description="Search for current news and information"
    )

    prompt =  """You are a helpful assistant with access to current web information. 
    When users ask about recent events or current information, use the web search tool.
    Always provide clear, well-formatted responses based on the search results."""

    os.environ["GROQ_API_KEY"] = groq_api_key
    llm = ChatGroq(model=model_name)
    tools = [custom_search]
    agent = create_react_agent(llm, tools=tools, prompt=prompt)

    def agent_node(state: State) -> State:
        response = agent.invoke({"messages": state['messages']})
        return {"messages": response["messages"]}
    

    graph_builder = StateGraph(State)
    conn = sqlite3.connect(database='chatbot.db', check_same_thread=False)
    memory = SqliteSaver(conn=conn)

    graph_builder.add_node("agent", agent_node)
    graph_builder.add_edge(START, "agent")
    graph_builder.add_edge("agent", END)

    graph = graph_builder.compile(checkpointer=memory)
    return graph, memory
