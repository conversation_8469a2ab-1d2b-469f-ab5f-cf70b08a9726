from langchain_groq import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing_extensions import TypedDict, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.prebuilt import create_react_agent
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain.agents import Tool
import sqlite3
import os
from dotenv import load_dotenv
load_dotenv()

def model_create(groq_api_key, model_name):

    ## web search tool
    search_tool = TavilySearchResults(tavily_api_key = os.getenv("TAVILY_API_KEY"))

    web_search = Tool(
        name="Web Search",
        func=search_tool.run,
        description="Search the web and return results with content, title, and answers."
    )
    tools = [web_search]

    os.environ["GROQ_API_KEY"] = groq_api_key
    llm = ChatGroq(model=model_name)
    agent = create_react_agent(llm, tools=tools)


    ### define state
    class State(TypedDict):
        messages: Annotated[list[BaseMessage], add_messages]

    graph_builder = StateGraph(State)

    ### define chatbot
    def chatbot(state: State):
        result = agent.invoke(state["messages"])
        return {"messages": result}

    
    ## database creation
    conn = sqlite3.connect(database='chatbot.db', check_same_thread=False)
    memory = SqliteSaver(conn = conn)

    graph_builder.add_node("chatbot", chatbot)
    graph_builder.add_edge(START, "chatbot") 
    graph_builder.add_edge("chatbot", END)      ### START -> chatbot -> END

    graph = graph_builder.compile(checkpointer=memory)
    return graph,memory