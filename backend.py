import streamlit as st
from langchain_groq import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON>plate
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.checkpoint.memory import InMemorySaver
import os

memory = InMemorySaver()

def model_create():
    with st.sidebar:
        ### getting api from user
        groq_api_key = st.text_input("enter you api key", type="password")
        os.environ["GROQ_API_KEY"] = groq_api_key

        ### select which model to use
        option = st.text_input("enter the model you want to use", value="gemma2-9b-it")
    llm = ChatGroq(model=option)
    return llm

llm = model_create()

### define state
class State(TypedDict):
    messages: Annotated[list[BaseMessage], add_messages]

graph_builder = StateGraph(State)

### define chatbot
def chatbot(state: State):
    return {"messages": [llm.invoke(state["messages"])]}

graph_builder.add_node("chatbot", chatbot)
graph_builder.add_edge(START, "chatbot") 
graph_builder.add_edge("chatbot", END)      ### START -> chatbot -> END

graph = graph_builder.compile(checkpointer=memory)