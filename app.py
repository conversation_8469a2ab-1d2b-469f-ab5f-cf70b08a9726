import streamlit as st
from backend import model_create
from langchain_core.messages import HumanMessage

thread_id = '1'
CONFIG = {'configurable':{'thread_id': thread_id}}

st.title("chatbot")

## user data collection
with st.sidebar:
    groq_api_key = st.text_input("Enter your API key", type="password")
    model_name = st.text_input("Enter the model you want to use", value="gemma2-9b-it")

graph = model_create(groq_api_key, model_name)

## display chat history
if "messages" not in st.session_state:
    st.session_state.messages = []

for message in st.session_state.messages:
    st.chat_message(message["role"]).text(message["content"])


### user input
user_input = st.chat_input("Enter your query")

if user_input:
    st.session_state.messages.append({"role": "user", "content": user_input})
    st.chat_message("user").text(user_input)

    with st.spinner("Generating response..."):
        value = graph.invoke({"messages": [HumanMessage(content=user_input)]}, config=CONFIG)
        st.session_state.messages.append({"role": "assistant", "content": value['messages'][-1].content})
        st.chat_message("assistant").text(value['messages'][-1].content)
