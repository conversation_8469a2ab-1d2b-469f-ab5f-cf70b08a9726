import streamlit as st
from agent.search import model_create
from utility import generate_unique_id, validate_groq_key, get_memory_for_mode
from langchain_core.messages import HumanMessage,AIMessage
from rag.agentic_rag import create_rag_chain
from database.get_sql import get_search_memory, get_rag_memory
import os

os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")
os.environ["LANGCHAIN_PROJECT"] = "chatbot"

st.title("chatbot")

## graph manager to manage rag and search graph
class GraphManager:
    def __init__(self, groq_api_key, model_name):
        self.groq_api_key = groq_api_key
        self.model_name = model_name
        self.search_graph = None
        self.rag_graph = None
        self.current_mode = "search"
        self.uploaded_docs = []
        
    def get_search_graph(self):
        if not self.search_graph:
            self.search_graph = model_create(self.groq_api_key, self.model_name)
        return self.search_graph
    
    def get_rag_graph(self, documents=None):
        if documents:
            self.uploaded_docs = documents
            self.rag_graph = create_rag_chain(self.groq_api_key, self.model_name, documents)
        return self.rag_graph
    
    def get_graph_for_mode(self, mode): # <-- Add this new method
        if mode == "rag":
            return self.get_rag_graph(self.uploaded_docs) # Assumes docs are loaded
        return self.get_search_graph()
    
    def get_current_graph(self):
        if self.current_mode == "rag" and self.rag_graph:
            return self.rag_graph
        return self.get_search_graph()
    
    def set_mode(self, mode):
        self.current_mode = mode

## user data collection
with st.sidebar:
    groq_api_key = st.text_input("Enter your API key", type="password")
    model_name = st.text_input("Enter the model you want to use", value="gemma2-9b-it")

## validate groq key
if groq_api_key and validate_groq_key(groq_api_key):
    if 'graph_manager' not in st.session_state:
        st.session_state.graph_manager = GraphManager(groq_api_key, model_name)
    graph_manager = st.session_state.graph_manager
else:
    st.error("Invalid Groq API key. Please check and try again.")
    st.stop()

user_input = None
input = st.chat_input("Enter your query", accept_file=True, file_type=['pdf'])

if input and input.text:
    user_input = input.text

if input and input.get('files'):
    documents = input['files']
    
    # Add to document list
    for doc in documents:
        if doc.name not in st.session_state.uploaded_documents:
            st.session_state.uploaded_documents.append(doc.name)
    
    # Create/update RAG graph
    st.session_state.graph_manager.get_rag_graph(documents)
    st.session_state.graph_manager.set_mode("rag")
    
    st.success(f"Uploaded {len(documents)} document(s). Now in RAG mode.")
    st.rerun()

memory = get_memory_for_mode(st.session_state.graph_manager.current_mode)

## function for storing all threads in the conversion
def add_thread_id(thread_id):
    if thread_id not in st.session_state['thread_list']:
        thread_id = f"{st.session_state.graph_manager.current_mode}_{thread_id}"
        st.session_state['thread_list'].append(thread_id)

## delete thread from sqlite database
def delete_thread(thread_id):
    mode = "rag" if thread_id.startswith("rag_") else "search"
    memory = get_memory_for_mode(mode)
    try:
        ## SqliteSaver doesn't have a direct delete method Need to access the underlying SQLite connection
        conn = memory.conn
        cursor = conn.cursor()
        
        ## Delete from checkpoints table (where conversation data is stored)
        cursor.execute("DELETE FROM checkpoints WHERE thread_id = ?", (thread_id,))
        conn.commit()
        
        return True
    except Exception as e:
        st.error(f"Error deleting thread: {e}")
        return False

## get the conversation from the database for the particular thread_id
def load_conversation(thread_id):
    mode = "rag" if thread_id.startswith("rag_") else "search"
    graph = st.session_state.graph_manager.get_graph_for_mode(mode)
    if not graph:
        st.error(f"Could not load graph for {mode} mode. Have you uploaded documents for this RAG chat?")
        return []
    state = graph.get_state(config={'configurable': {'thread_id': thread_id}})
    return state.values.get('messages', [])

## retrieve all threads from sqlite database
def retrieve_threads():
    search_memory = get_search_memory()
    rag_memory = get_rag_memory()
    all_threads = set()

    for m in search_memory.list(None):
        all_threads.add(m.config['configurable']['thread_id'])
    for m in rag_memory.list(None):
        all_threads.add(m.config['configurable']['thread_id'])

    return list(all_threads)

## setup for session state
if "messages" not in st.session_state:
    st.session_state.messages = []

if 'thread_list' not in st.session_state:
    st.session_state['thread_list'] = retrieve_threads()

if 'thread_id' not in st.session_state:
    st.session_state['thread_id'] = f"search_{generate_unique_id()}"

## function to extract first message from thread
def get_thread_preview(thread_id):
    try:
        # Try search first
        messages = load_conversation(thread_id, "search")
        if not messages:
            # Try RAG
            messages = load_conversation(thread_id, "rag")
        
        if messages:
            first_msg = messages[0]
            if hasattr(first_msg, 'content'):
                content = first_msg.content[:30] + "..." if len(first_msg.content) > 30 else first_msg.content
                return f"{content}"
        return f"Thread {thread_id[:8]}"
    except:
        return f"Thread {thread_id[:8]}"

def delete_document(doc_name, index):
    try:
        # Remove from session state
        st.session_state.uploaded_documents.pop(index)
        
        # Clear vector store if no documents left
        if not st.session_state.uploaded_documents:
            clear_vector_store()
            st.session_state.graph_manager.rag_graph = None
            st.session_state.graph_manager.current_mode = "search"
        
        st.success(f"Deleted {doc_name}")
    except Exception as e:
        st.error(f"Error deleting document: {e}")

def clear_vector_store():
    import shutil
    vector_store_path = "./chroma_langchain_db"
    if os.path.exists(vector_store_path):
        shutil.rmtree(vector_store_path)


def clear_all_documents():
    """Clear all documents and vector store"""
    try:
        st.session_state.uploaded_documents = []
        clear_vector_store()
        st.session_state.graph_manager.rag_graph = None
        st.session_state.graph_manager.current_mode = "search"
        st.success("All documents cleared")
    except Exception as e:
        st.error(f"Error clearing documents: {e}")

# In app.py - Add document management
with st.sidebar:
    st.subheader("📄 Document Management")
    
    # Show uploaded documents
    if 'uploaded_documents' not in st.session_state:
        st.session_state.uploaded_documents = []
    
    if st.session_state.uploaded_documents:
        st.write("Uploaded Documents:")
        for i, doc_name in enumerate(st.session_state.uploaded_documents):
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"📄 {doc_name}")
            with col2:
                if st.button("🗑️", key=f"del_doc_{i}", help=f"Delete {doc_name}"):
                    delete_document(doc_name, i)
                    st.rerun()
    
    # Clear all documents
    if st.session_state.uploaded_documents:
        if st.button("🗑️ Clear All Documents"):
            clear_all_documents()
            st.rerun()

## Enhanced thread management in sidebar
with st.sidebar:
    st.subheader("Thread Management")
    
    ## New chat button
    if st.button("🆕 New Chat"):
        thread = generate_unique_id()
        st.session_state['thread_id'] = f"{st.session_state.graph_manager.current_mode}_{thread}"
        st.session_state.messages = []
        st.rerun()
    
    ## Thread selection with delete option
    if st.session_state.get('thread_list'):
        col1, col2 = st.columns([3, 1])
        
        with col1:
            selected_thread = st.selectbox(
                "Select conversation",
                options=st.session_state['thread_list'][::-1],
                format_func=get_thread_preview
            )
        
        with col2: ## delete the thread instantly before thread is loaded
            if st.button("🗑️", help="Delete selected thread"):
                if selected_thread != st.session_state['thread_id']:
                    if delete_thread(selected_thread, memory):
                        st.session_state['thread_list'].remove(selected_thread)
                        st.success("Thread deleted!")
                        st.rerun()
                else:
                    st.error("Cannot delete current thread")


## Add to sidebar (remove any empty threads which were created) keeps the thread list clean
with st.expander("🧹 Thread Cleanup"):
    st.write(f"Total threads: {len(st.session_state.get('thread_list', []))}")
    
    ## check thread where messages are empty and delete them
    if st.button("Delete All Empty Threads"):
        deleted_count = 0
        for thread_id in st.session_state['thread_list'].copy():
            if thread_id != st.session_state['thread_id']: ## don't delete current thread
                messages = load_conversation(thread_id)
                if not messages:  # Empty thread
                    if delete_thread(thread_id, memory):
                        st.session_state['thread_list'].remove(thread_id)
                        deleted_count += 1
        
        if deleted_count > 0:
            st.success(f"Deleted {deleted_count} empty threads")
            st.rerun()

## load conversation from selected thread
if selected_thread != st.session_state['thread_id']:
    st.session_state['thread_id'] = selected_thread
    messages = load_conversation(selected_thread)
    conv = []
    for m in messages:
        if isinstance(m, HumanMessage):  ### problem messages are not in the humanmessage or aimessage format
            conv.append({"role": "user", "content": m.content})
        else:
            conv.append({"role": "assistant", "content": m.content})
    st.session_state.messages = conv
    st.rerun()


## display chat history
for message in st.session_state.messages:
    st.chat_message(message["role"]).markdown(message["content"])

if user_input:
    # Determine which graph to use
    current_graph = st.session_state.graph_manager.get_current_graph()
    st.chat_message("user").markdown(user_input)
    st.session_state.messages.append({"role": "user", "content": user_input})
    if st.session_state.thread_id not in st.session_state.thread_list:
        st.session_state.thread_list.append(st.session_state.thread_id)
    
    # Use appropriate config based on mode
    mode = st.session_state.graph_manager.current_mode
    graph_input = {
        "question": user_input,
        "messages": [HumanMessage(content=m["content"]) if m["role"] == "user" else AIMessage(content=m["content"]) for m in st.session_state.messages]
    }
    st.session_state.messages.append({"role": "user", "content": user_input})
    CONFIG = {'configurable': {'thread_id': st.session_state.thread_id}}

    with st.chat_message("assistant"):
        with st.spinner("Generating response..."):
            ai_message = current_graph.invoke({"question": graph_input["question"], "messages": graph_input["messages"]}, config=CONFIG)
            st.markdown(ai_message["generation"])
            st.session_state.messages.append({"role": "assistant", "content": ai_message["generation"]})