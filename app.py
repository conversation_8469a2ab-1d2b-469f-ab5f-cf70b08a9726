import streamlit as st
from backend import model_create
from utility import generate_unique_id, validate_groq_key
from langchain_core.messages import HumanMessage

st.title("chatbot")

## user data collection
with st.sidebar:
    groq_api_key = st.text_input("Enter your API key", type="password")
    model_name = st.text_input("Enter the model you want to use", value="gemma2-9b-it")

## validate groq key
is_valid_key = False
if groq_api_key:
    if validate_groq_key(groq_api_key):
        is_valid_key = True
    else:
        st.error("Invalid Groq API key. Please check and try again.")

## create graph
graph = model_create(groq_api_key, model_name)

## function for storing all threads in the conversion
def add_thread_id(thread_id):
    if 'thread_list' not in st.session_state:
        st.session_state['thread_list'] = []
    if thread_id not in st.session_state['thread_list']:
        st.session_state['thread_list'].append(thread_id)

def load_conversation(thread_id):
    return graph.get_state(config = {'configurable':{'thread_id': thread_id}}).values['messages']

if 'thread_id' not in st.session_state:
    st.session_state['thread_id'] = generate_unique_id()
add_thread_id(st.session_state['thread_id'])

## config for langgraph
CONFIG = {'configurable':{'thread_id': st.session_state['thread_id']}}

## new chat button
if st.sidebar.button("new chat"):
    st.session_state['thread_id'] = generate_unique_id()
    st.session_state.messages = []
    CONFIG = {'configurable':{'thread_id': st.session_state['thread_id']}}
    add_thread_id(st.session_state['thread_id'])

for thread_id in st.session_state['thread_list']:
    if st.sidebar.button(thread_id):
        st.session_state['thread_id'] = thread_id
        messages = load_conversation(thread_id)
        temp_messages = []

        for message in messages:
            temp_messages.append({"role": message["role"], "content": message["content"]})
        st.session_state.messages = temp_messages


## display chat history
if "messages" not in st.session_state:
    st.session_state.messages = []

for message in st.session_state.messages:
    st.chat_message(message["role"]).text(message["content"])

### user input
user_input = st.chat_input("Enter your query")

if user_input:
    st.session_state.messages.append({"role": "user", "content": user_input})
    st.chat_message("user").markdown(user_input)

    with st.chat_message("assistant"):
        with st.spinner("Generating response..."):
            ai_message = st.write_stream(
                message_chunk.content for message_chunk, metadata in graph.stream(
                    {"messages": [HumanMessage(content=user_input)]}, 
                    config=CONFIG, 
                    stream_mode="messages"
                )
            )
        st.session_state.messages.append({"role": "assistant", "content": ai_message})
