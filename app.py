import streamlit as st
from backend import graph
from langchain_core.messages import HumanMessage

thread_id = '1'
CONFIG = {'configurable':{'thread_id': thread_id}}

st.title("chatbot")


## display chat history
if "messages" not in st.session_state:
    st.session_state.messages = []

for message in st.session_state.messages:
    st.chat_message(message["role"]).text(message["content"])


### user input
user_input = st.chat_input("Enter your query")

if user_input:
    st.session_state.messages.append({"role": "user", "content": user_input})
    st.chat_message("user").text(user_input)

    with st.spinner("Generating response..."):
        value = graph.invoke({"messages": [HumanMessage(content=user_input)]}, config=CONFIG)
        st.session_state.messages.append({"role": "assistant", "content": value['messages'][-1].content})
        st.chat_message("assistant").text(value['messages'][-1].content)
