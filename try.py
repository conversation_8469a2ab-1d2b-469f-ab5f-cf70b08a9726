from langchain_groq import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing_extensions import TypedDict, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.prebuilt import create_react_agent
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_core.tools import tool
from langgraph.checkpoint.memory import InMemorySaver
import streamlit as st
from dotenv import load_dotenv
load_dotenv()

config = {"configurable": {"thread_id": "1"}}

## define state
class State(TypedDict):
    messages: Annotated[list[BaseMessage], add_messages]

search_tool = TavilySearchResults()

@tool
def formatted_web_search(query: str) -> str:
    """Search for current news and information on the web.

Args:
    query: The search query to look up current information
    
Returns:
    Formatted search results with titles, content, and sources
"""
    raw_results = search_tool.run(query)
    
    if not raw_results:
        return "No current information found for this query."
    
    formatted = f"Here's what I found about '{query}':\n\n"
    
    for i, result in enumerate(raw_results[:3], 1):
        title = result.get("title", "")
        content = result.get("content", "")
        url = result.get("url", "")
        
        formatted += f"**{i}. {title}**\n{content}\n*Source: {url}*\n\n"
    
    return formatted


prompt =  """You are a helpful assistant with access to current web information. 
When users ask about recent events or current information, use the web search tool.
Always provide clear, well-formatted responses based on the search results.
when use web search tool then use the following format:
**title**
content, content should be only content important points and should be less than 100 words.
*Source: url*
"""

llm = ChatGroq(model="gemma2-9b-it")
tools = [formatted_web_search]
agent = create_react_agent(llm, tools=tools, prompt=prompt)

def agent_node(state: State) -> State:
    response = agent.invoke({"messages": state['messages']})
    return {"messages": response["messages"]}


graph_builder = StateGraph(State)

memory = InMemorySaver()

graph_builder.add_node("agent", agent_node)
graph_builder.add_edge(START, "agent")
graph_builder.add_edge("agent", END)

graph = graph_builder.compile(checkpointer=memory)

response = graph.invoke({'messages': [HumanMessage(content="tell me top 5 latest news world wide")]}, config=config)

ai_message = next(
    (msg for msg in reversed(response["messages"]) if isinstance(msg, AIMessage)),
    None
)

if ai_message:
    st.markdown(ai_message.content)

class AgentState(TypedDict):
    messages: Annotated[list[BaseMessage], add_messages]

graph = StateGraph(AgentState)

def agent(state: AgentState) -> AgentState:
    response = agent_executor.invoke({"messages": state['messages']})
    return {"messages": response["messages"]}

graph.add_node("agent", agent)
graph.add_edge(START, "agent")
graph.add_edge("agent", END)

workflow = graph.compile()
workflow