from langchain_groq import <PERSON><PERSON><PERSON><PERSON>q
from langchain_tavily import <PERSON>lySearch
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage
from dotenv import load_dotenv
from langchain_community.tools import DuckDuckGoSearchRun
import streamlit as st

# Load environment variables
load_dotenv()

# Set up tools and LLM
search_tool = TavilySearch()
# search_tool = DuckDuckGoSearchRun()
tools = [search_tool]

llm = ChatGroq(model="openai/gpt-oss-20b")

# Create the agent executor with tools and a custom prompt
agent_executor = create_react_agent(
    model=llm,
    tools=tools,
    prompt="You are a helpful assistant. If you don't know the answer, try using tools to find the answer."
)

# Define user message
user_input = "tell me top news from the india (14th august 2025) "

for step in agent_executor.stream(
    {"messages": user_input},
    stream_mode="values",
):
    st.write(step["messages"][-1].content)